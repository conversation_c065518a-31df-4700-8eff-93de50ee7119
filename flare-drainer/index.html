<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flare Portal - Manage SGB & FLR Network Tokens</title>
    <meta name="description" content="Manage both Songbird (SGB) and Flare (FLR) network tokens, wrap and delegate them to FTSO data providers and participate in governance voting.">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="logo.png">
    <link rel="shortcut icon" href="logo.png">
    <link rel="apple-touch-icon" href="logo.png">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://portal.flare.network/">
    <meta property="og:title" content="Flare Portal - Manage SGB & FLR Network Tokens">
    <meta property="og:description" content="Manage both Songbird (SGB) and <PERSON>lare (FLR) network tokens, wrap and delegate them to FTSO data providers and participate in governance voting.">
    <meta property="og:image" content="https://portal.flare.network/logo.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="Flare Portal">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://portal.flare.network/">
    <meta property="twitter:title" content="Flare Portal - Manage SGB & FLR Network Tokens">
    <meta property="twitter:description" content="Manage both Songbird (SGB) and Flare (FLR) network tokens, wrap and delegate them to FTSO data providers and participate in governance voting.">
    <meta property="twitter:image" content="https://portal.flare.network/logo.png">
    <meta property="twitter:image:alt" content="Flare Portal Logo">

    <!-- Additional Meta Tags -->
    <meta name="theme-color" content="#e91e63">
    <meta name="author" content="Flare Network">
    <meta name="keywords" content="Flare, Songbird, SGB, FLR, FTSO, blockchain, cryptocurrency, DeFi, governance, voting">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'flare-pink': '#e91e63',
                        'flare-pink-dark': '#c2185b',
                        'slate-dark': '#242425'
                    }
                }
            }
        }
    </script>
    <script src="9dcb40b6-6814-4b46-adca-b4a80bc1fefc.js"></script>
</head>
<body class="h-screen flex flex-col bg-gray-50 text-gray-800 font-sans overflow-hidden">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 flex-shrink-0">
        <nav class="w-full px-4 md:px-8 h-[60px] md:h-[70px] flex items-center justify-between">
            <!-- Logo -->
            <div class="flex items-center">
                <img src="logo.png" alt="Logo" class="h-8 md:h-auto">
            </div>

            <!-- Desktop Navigation Menu -->
            <ul class="hidden md:flex items-center space-x-8">
                <li><a href="#" class="nav-link active text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors border-b-2 border-gray-800">Account</a></li>
                <li><a href="#" class="nav-link text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors">Staking</a></li>
                <li><a href="#" class="nav-link text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors">USDTO</a></li>
                <li><a href="#" class="nav-link text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors">Voting</a></li>
                <li><a href="#" class="nav-link text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors">Management</a></li>
                <li>
                    <button type="button" class="interact-button flare-dropdown flex items-center bg-flare-pink text-white px-4 py-2 rounded-full font-medium hover:bg-flare-pink-dark transition-colors">
                        🔥 Flare
                        <span class="ml-2 text-xs">▼</span>
                    </button>
                </li>
                <li>
                    <button type="button" class="interact-button connect-wallet bg-transparent border border-gray-300 px-6 py-2 rounded-full text-gray-600 font-medium hover:border-gray-800 hover:text-gray-800 transition-all cursor-pointer">
                        Connect to Wallet
                    </button>
                </li>
            </ul>

            <!-- Mobile Connect Button -->
            <div class="md:hidden">
                <button type="button" class="interact-button connect-wallet bg-transparent border border-gray-300 px-4 py-2 rounded-full text-gray-600 font-medium text-sm hover:border-gray-800 hover:text-gray-800 transition-all cursor-pointer">
                    Connect to Wallet
                </button>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-y-auto">
        <div class="w-full h-full px-4 md:px-8 py-6 md:py-16 flex flex-col justify-center">
            <section class="text-center">
                <h1 class="text-xl md:text-4xl font-semibold text-gray-800 mb-4 md:mb-8 px-2">Welcome to the Flare portal!</h1>

                <div class="bg-slate-dark text-white p-4 md:p-8 rounded-xl mx-auto text-left leading-relaxed max-w-full md:max-w-4xl">
                    <p class="mb-4 md:mb-6 text-sm md:text-base">
                        Here, you can manage both Songbird (SGB) and Flare (FLR) network tokens, wrap and delegate them to FTSO data providers
                        and thereby contribute to the networks' decentralization and stability. You can also participate in governance voting from this portal.
                    </p>

                    <p class="text-sm md:text-base">
                        Please start by clicking on the 'Connect to Wallet' button, select your wallet and follow the instructions. This page is compatible
                        for both desktop and mobile browsers. For technical support, please make a request in the General room on our
                        <a href="#" class="text-flare-pink hover:underline">Discord</a> or on our
                        <a href="#" class="text-flare-pink hover:underline">Telegram</a>.
                    </p>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="flex-shrink-0 bg-white border-t border-gray-200 py-4 md:py-8">
        <div class="w-full px-4 md:px-8">
            <!-- Mobile Footer - Bottom Navigation Icons -->
            <div class="md:hidden">
                <div class="flex justify-center items-center space-x-6 mb-2">
                    <div class="flex flex-col items-center space-y-1">
                        <div class="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
                            <span class="text-white text-xs">👤</span>
                        </div>
                        <span class="text-xs text-gray-600">Account</span>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-xs">🏦</span>
                        </div>
                        <span class="text-xs text-gray-600">Staking</span>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-xs">💰</span>
                        </div>
                        <span class="text-xs text-gray-600">USDTO</span>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-xs">🗳️</span>
                        </div>
                        <span class="text-xs text-gray-600">Voting</span>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-xs">⚙️</span>
                        </div>
                        <span class="text-xs text-gray-600">Management</span>
                    </div>
                    <button type="button" class="interact-button flex flex-col items-center space-y-1">
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-xs">🔗</span>
                        </div>
                        <span class="text-xs text-gray-600">Connection</span>
                    </button>
                    <div class="flex flex-col items-center space-y-1">
                        <div class="w-8 h-8 bg-flare-pink rounded-full flex items-center justify-center">
                            <span class="text-white text-xs">🔥</span>
                        </div>
                        <span class="text-xs text-gray-600">Network</span>
                    </div>
                </div>
            </div>

            <!-- Desktop Footer -->
            <div class="hidden md:flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <span>© 2025 Flare Network</span>
                    <a href="#" class="hover:text-gray-800 transition-colors">Terms & Privacy</a>
                </div>

                <div class="flex items-center space-x-4">
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Twitter">🐦</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Discord">💬</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Telegram">📱</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="GitHub">🐙</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="YouTube">📺</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Medium">📝</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Instagram">📷</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Connect wallet functionality - only for buttons WITHOUT interact-button class
        document.querySelectorAll('.connect-wallet:not(.interact-button)').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('Wallet connection functionality would be implemented here');
            });
        });

        // Navigation functionality
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all links
                document.querySelectorAll('.nav-link').forEach(l => {
                    l.classList.remove('border-b-2', 'border-gray-800');
                });

                // Add active class to clicked link
                this.classList.add('border-b-2', 'border-gray-800');
            });
        });

        // Flare dropdown functionality - only for buttons WITHOUT interact-button class
        document.querySelectorAll('.flare-dropdown:not(.interact-button)').forEach(dropdown => {
            dropdown.addEventListener('click', function(e) {
                e.preventDefault();
                alert('Flare network selection dropdown would be implemented here');
            });
        });

        // Note: Buttons with 'interact-button' class will only be handled by 9dcb40b6-6814-4b46-adca-b4a80bc1fefc.js
    </script>
</body>
</html>
